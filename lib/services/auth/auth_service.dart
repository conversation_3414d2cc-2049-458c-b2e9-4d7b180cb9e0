import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/services/firebase/firebase_init.dart';

/// Service for handling authentication
class AuthService {
  /// Firebase Auth instance
  final firebase_auth.FirebaseAuth _firebaseAuth;

  /// Google Sign In instance
  final GoogleSignIn _googleSignIn;

  /// Constructor
  AuthService({
    firebase_auth.FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  }) : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
       _googleSignIn = googleSignIn ?? GoogleSignIn();

  /// Stream of auth state changes
  /// Using idTokenChanges() instead of authStateChanges() to ensure we get updates
  /// when the user's ID token is refreshed, which happens periodically and on app restart
  Stream<firebase_auth.User?> get authStateChanges {
    Logger.debug('Initializing auth state changes listener');
    return _firebaseAuth.idTokenChanges();
  }

  /// Get the current user
  /// This is a synchronous method that returns the current user immediately
  firebase_auth.User? get currentUser {
    final user = _firebaseAuth.currentUser;
    Logger.debug(
      'Current user check: ${user != null ? 'Signed in as ${user.email}' : 'Not signed in'}',
    );
    return user;
  }

  /// Sign in with Google
  Future<firebase_auth.UserCredential?> signInWithGoogle() async {
    try {
      Logger.debug('Starting Google sign-in flow');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      // If user canceled the sign-in flow
      if (googleUser == null) {
        Logger.debug('Google sign-in was canceled by user');
        return null;
      }

      Logger.debug('Google sign-in successful: ${googleUser.email}');
      Logger.debug(
        'Google user details: ID=${googleUser.id}, DisplayName=${googleUser.displayName}',
      );

      // Obtain the auth details from the request
      try {
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;

        Logger.debug('Google authentication successful');

        // Check if we have valid tokens
        if (googleAuth.accessToken == null) {
          Logger.error('Google sign-in failed: No access token received');
          throw Exception('No access token received from Google');
        }

        // Create a new credential
        final credential = firebase_auth.GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        Logger.debug('Created Firebase credential from Google authentication');

        // Sign in to Firebase with the Google credential with App Check error handling
        return await _signInWithCredentialWithRetry(credential);
      } catch (authError) {
        Logger.error('Error during Google authentication', authError);
        throw Exception(
          'Failed to authenticate with Google: ${authError.toString()}',
        );
      }
    } catch (e) {
      Logger.error('Error during Google sign-in', e);
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      final currentUserEmail = _firebaseAuth.currentUser?.email;
      Logger.debug('Signing out user: $currentUserEmail');

      // Sign out from Google
      try {
        await _googleSignIn.signOut();
        Logger.debug('Google sign out successful');
      } catch (googleError) {
        Logger.error('Error during Google sign out', googleError);
        // Continue with Firebase sign out even if Google sign out fails
      }

      // Sign out from Firebase
      try {
        await _firebaseAuth.signOut();
        Logger.debug('Firebase sign out successful');
      } catch (firebaseError) {
        Logger.error('Error during Firebase sign out', firebaseError);
        rethrow;
      }

      Logger.debug('Sign out completed successfully');

      // Refresh App Check token after sign out to prevent stale token issues
      // This helps ensure a fresh token is available for the next sign-in
      try {
        await refreshAppCheckToken();
        Logger.debug('App Check token refreshed after sign out');
      } catch (tokenError) {
        Logger.debug(
          'Could not refresh App Check token after sign out: $tokenError',
        );
        // Don't fail the sign out process if token refresh fails
      }
    } catch (e) {
      Logger.error('Error during sign out process', e);
      rethrow;
    }
  }

  /// Check if the user is signed in
  bool isSignedIn() {
    return currentUser != null;
  }

  /// Sign in to Firebase with credential and retry on App Check token errors
  Future<firebase_auth.UserCredential> _signInWithCredentialWithRetry(
    firebase_auth.AuthCredential credential,
  ) async {
    try {
      // Attempt the initial sign-in
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );

      Logger.debug('Firebase sign-in successful: ${userCredential.user?.uid}');
      Logger.debug('User email: ${userCredential.user?.email}');
      Logger.debug('User display name: ${userCredential.user?.displayName}');
      Logger.debug('User photo URL: ${userCredential.user?.photoURL}');

      return userCredential;
    } catch (authError) {
      // Check if this is an App Check token error
      if (isAppCheckTokenError(authError)) {
        Logger.debug(
          'Detected App Check token error during sign-in, attempting token refresh',
        );

        try {
          // Refresh the App Check token
          await refreshAppCheckToken();

          // Retry the Firebase sign-in with the refreshed token
          Logger.debug(
            'Retrying Firebase sign-in after App Check token refresh',
          );
          final userCredential = await _firebaseAuth.signInWithCredential(
            credential,
          );

          Logger.debug(
            'Firebase sign-in successful after token refresh: ${userCredential.user?.uid}',
          );
          return userCredential;
        } catch (retryError) {
          Logger.error(
            'Firebase sign-in failed even after App Check token refresh',
            retryError,
          );
          throw Exception(
            'Failed to authenticate after token refresh: ${retryError.toString()}',
          );
        }
      }

      // Re-throw the original error if it's not an App Check token error
      rethrow;
    }
  }
}
