import 'dart:convert';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:noeji/models/chat_message.dart';
import 'package:noeji/services/firebase/cloud_function_service.dart';
import 'package:noeji/utils/logger.dart';

/// Response from the Grok chat API
class ChatResponse {
  /// Whether the request was successful
  final bool isSuccess;

  /// The response message content
  final String? content;

  /// Error message if the request failed
  final String? errorMessage;

  /// Creates a new ChatResponse for a successful request
  const ChatResponse.success({required this.content})
    : isSuccess = true,
      errorMessage = null;

  /// Creates a new ChatResponse for a failed request
  const ChatResponse.failure({required this.errorMessage})
    : isSuccess = false,
      content = null;
}

/// Service for interacting with the Grok API
/// This service is currently not used but kept for potential future use.
/// The app now uses GemmaService for chat functionality.
class GrokService {
  /// The model to use for chat
  final String model;

  /// The reasoning effort level
  final String reasoningEffort;

  /// The Cloud Function service
  final CloudFunctionService _cloudFunctionService;

  /// Constructor
  GrokService({
    this.model = 'grok-3-mini-beta',
    this.reasoningEffort = 'low',
    CloudFunctionService? cloudFunctionService,
  }) : _cloudFunctionService = cloudFunctionService ?? CloudFunctionService();

  /// Send a chat request to the Grok API via Firebase Cloud Function
  /// Limits the number of messages sent to the LLM to the last 25 messages
  /// to save LLM token input and reduce API costs
  Future<ChatResponse> chat(List<ChatMessage> messages) async {
    try {
      Logger.debug(
        'Sending chat request to Grok API via Firebase Cloud Function',
      );

      // Limit to the last 25 messages to save LLM token input and API cost
      final limitedMessages =
          messages.length > 25
              ? messages.sublist(messages.length - 25)
              : messages;

      // Format messages for the API
      final formattedMessages =
          limitedMessages
              .map(
                (message) => {
                  'role':
                      message.role == MessageRole.user ? 'user' : 'assistant',
                  'content': message.content,
                },
              )
              .toList();

      // Log the messages for debugging
      if (messages.length > 25) {
        Logger.debug(
          'Limited chat history from ${messages.length} to 25 messages to save LLM token input and API cost',
        );
      }
      Logger.debug('Formatted messages for Grok API:');
      for (var msg in formattedMessages) {
        Logger.debug('Role: ${msg['role']}, Content: ${msg['content']}');
      }

      // Create the request body
      final requestBody = {
        'messages': formattedMessages,
        'reasoning_effort': reasoningEffort,
        'model': model,
      };

      Logger.debug('Grok API request body: $requestBody');

      // Call the Firebase Cloud Function
      final response = await _cloudFunctionService.callLlmApi(
        provider: 'grok',
        requestBody: requestBody,
      );

      if (!response.isSuccess || response.data == null) {
        Logger.error(
          'Grok API error via Firebase Cloud Function: ${response.errorMessage}',
        );

        // Log detailed error information if available
        if (response.details != null) {
          final location =
              response.details!['location'] as String? ?? 'unknown';
          final timestamp = response.details!['timestamp'] as String? ?? '';
          final stack = response.details!['stack'] as String? ?? '';

          Logger.error('Error details - Location: $location, Time: $timestamp');
          Logger.error('Stack trace: $stack');
        }

        return ChatResponse.failure(
          errorMessage: response.errorMessage ?? 'Unknown error',
        );
      }

      // Parse the response
      final jsonResponse = response.data;
      Logger.debug(
        'Grok API response via Firebase Cloud Function: $jsonResponse',
      );

      // Extract the message content
      final content = jsonResponse['choices'][0]['message']['content'];

      // Ensure proper encoding of the content
      // This helps with non-ASCII characters like Chinese
      final decodedContent = _ensureProperEncoding(content);

      return ChatResponse.success(content: decodedContent);
    } catch (e) {
      Logger.error('Error in Grok chat request via Firebase Cloud Function', e);

      // Create a more detailed error message
      String errorMessage = 'Error in chat request: $e';

      // Check if it's a FirebaseFunctionsException
      if (e is FirebaseFunctionsException) {
        // Extract more details if available
        final details = e.details as Map<String, dynamic>?;
        if (details != null) {
          final location = details['location'] as String? ?? 'unknown';
          final timestamp = details['timestamp'] as String? ?? '';
          final stack = details['stack'] as String? ?? '';

          Logger.error('Firebase Function error details:');
          Logger.error('Location: $location');
          Logger.error('Timestamp: $timestamp');
          Logger.error('Stack: $stack');

          // Create a more informative error message
          errorMessage = 'Error in $location: ${e.message}';
        }
      }

      return ChatResponse.failure(errorMessage: errorMessage);
    }
  }

  /// Ensure proper encoding of the content
  /// This helps with non-ASCII characters like Chinese
  String _ensureProperEncoding(String content) {
    try {
      // First, check if the content is already valid UTF-8
      if (_isValidUtf8(content)) {
        return content;
      }

      // If not, try to decode and re-encode the content
      final decoded = utf8.decode(utf8.encode(content));
      return decoded;
    } catch (e) {
      Logger.error('Error ensuring proper encoding: $e');
      return content; // Return original content if encoding fails
    }
  }

  /// Check if a string is valid UTF-8
  bool _isValidUtf8(String text) {
    try {
      utf8.decode(utf8.encode(text));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Dispose method (kept for backward compatibility)
  void dispose() {
    // No HTTP client to dispose anymore
  }
}
