import 'dart:convert';
import 'package:firebase_ai/firebase_ai.dart';
import 'package:noeji/models/chat_message.dart';
import 'package:noeji/services/llm/firebase_ai_provider.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Response from a chat request
class ChatResponse {
  /// Whether the request was successful
  final bool isSuccess;

  /// The content of the response
  final String? content;

  /// The error message if the request failed
  final String? errorMessage;

  /// The original JSON response from the LLM
  final Map<String, dynamic>? originalJson;

  /// Constructor for successful response
  const ChatResponse.success({this.content, this.originalJson})
    : isSuccess = true,
      errorMessage = null;

  /// Constructor for failed response
  const ChatResponse.failure({required this.errorMessage})
    : isSuccess = false,
      content = null,
      originalJson = null;
}

/// Enum for message roles
enum MessageRole {
  /// User message
  user,

  /// Assistant message
  assistant,
}

/// Service for interacting with Gemini 2.0 Flash Lite model for chat functionality
class GeminiChatService {
  /// The Firebase AI provider
  final FirebaseAiProvider _firebaseAiProvider;

  /// LLM model config provider
  final LlmModelConfig _llmModelConfig;

  /// User tier for accessing appropriate prompts and configs
  final String? _userTier;

  /// Constructor
  GeminiChatService({
    FirebaseAiProvider? firebaseAiProvider,
    required LlmModelConfig llmModelConfig,
    String? userTier,
  }) : _firebaseAiProvider = firebaseAiProvider ?? FirebaseAiProvider(),
       _llmModelConfig = llmModelConfig,
       _userTier = userTier;

  /// Dispose resources
  void dispose() {
    // No resources to dispose
  }

  /// Helper to convert ChatMessage list to Content list for Firebase AI SDK
  List<Content> _formatMessagesForSdk(List<ChatMessage> messages) {
    return messages.map((message) {
      final role = message.role.toString() == 'MessageRole.user' ? 'user' : 'model';
      return Content(role, [TextPart(message.content)]);
    }).toList();
  }

  /// Ensure proper encoding of the content
  String _ensureProperEncoding(String content) {
    try {
      // Try to decode and re-encode to ensure proper UTF-8 handling
      return utf8.decode(utf8.encode(content));
    } catch (e) {
      Logger.error('Error ensuring proper encoding', e);
      return content; // Return original if encoding fails
    }
  }

  /// Clean JSON content to fix common formatting issues
  String _cleanJsonContent(String content) {
    try {
      // Remove any leading/trailing whitespace
      String cleaned = content.trim();

      // Fix common issues with JSON strings containing unescaped characters
      // Replace literal newlines with escaped newlines in JSON string values
      cleaned = _escapeJsonStringValues(cleaned);

      // Remove any trailing commas before closing braces/brackets
      cleaned = cleaned.replaceAll(RegExp(r',(\s*[}\]])'), r'$1');

      return cleaned;
    } catch (e) {
      Logger.debug('Error cleaning JSON content: $e');
      return content;
    }
  }

  /// Escape special characters in JSON string values
  String _escapeJsonStringValues(String content) {
    try {
      // This is a simplified approach to escape newlines and other control characters
      // in JSON string values while preserving the JSON structure

      // Split by quotes to identify string values
      final parts = <String>[];
      bool inString = false;
      String current = '';

      for (int i = 0; i < content.length; i++) {
        final char = content[i];

        if (char == '"' && (i == 0 || content[i - 1] != '\\')) {
          // Toggle string state
          inString = !inString;
          current += char;

          if (!inString) {
            // End of string, add to parts
            parts.add(current);
            current = '';
          }
        } else if (inString) {
          // Inside a string, escape special characters
          switch (char) {
            case '\n':
              current += '\\n';
              break;
            case '\r':
              current += '\\r';
              break;
            case '\t':
              current += '\\t';
              break;
            case '\\':
              if (i + 1 < content.length &&
                  content[i + 1] != 'n' &&
                  content[i + 1] != 'r' &&
                  content[i + 1] != 't' &&
                  content[i + 1] != '"' &&
                  content[i + 1] != '\\') {
                current += '\\\\';
              } else {
                current += char;
              }
              break;
            default:
              current += char;
          }
        } else {
          // Outside string, add as-is
          current += char;
        }
      }

      // Add any remaining content
      if (current.isNotEmpty) {
        parts.add(current);
      }

      return parts.join('');
    } catch (e) {
      Logger.debug('Error escaping JSON string values: $e');
      return content;
    }
  }

  /// Extract JSON fields manually using regex as a fallback
  Map<String, dynamic>? _extractJsonFieldsManually(String content) {
    try {
      final result = <String, dynamic>{};

      // Extract user_prompt field
      final userPromptMatch = RegExp(
        r'"user_prompt"\s*:\s*"([^"]*(?:\\.[^"]*)*)"',
        dotAll: true,
      ).firstMatch(content);
      if (userPromptMatch != null) {
        result['user_prompt'] =
            userPromptMatch.group(1)?.replaceAll(r'\"', '"') ?? '';
      }

      // Extract response field - this is more complex as it can contain newlines and quotes
      final responseMatch = RegExp(
        r'"response"\s*:\s*"(.*)"(?:\s*}?\s*$)',
        dotAll: true,
      ).firstMatch(content);
      if (responseMatch != null) {
        String responseContent = responseMatch.group(1) ?? '';

        // Clean up the response content
        responseContent = responseContent
            .replaceAll(r'\"', '"') // Unescape quotes
            .replaceAll(
              r'\\n',
              '\n',
            ) // Convert escaped newlines to actual newlines
            .replaceAll(r'\\r', '\r') // Convert escaped carriage returns
            .replaceAll(r'\\t', '\t') // Convert escaped tabs
            .replaceAll(r'\\\\', '\\'); // Convert escaped backslashes

        // Remove trailing quote and brace if present
        if (responseContent.endsWith('"}')) {
          responseContent = responseContent.substring(
            0,
            responseContent.length - 2,
          );
        } else if (responseContent.endsWith('"')) {
          responseContent = responseContent.substring(
            0,
            responseContent.length - 1,
          );
        }

        result['response'] = responseContent;
      }

      // Return result if we found at least one field
      if (result.isNotEmpty) {
        Logger.debug(
          'Manual extraction found ${result.keys.length} fields: ${result.keys.join(', ')}',
        );
        return result;
      }

      return null;
    } catch (e) {
      Logger.debug('Error in manual JSON field extraction: $e');
      return null;
    }
  }

  /// Send a chat request to the Gemini API via Firebase AI Logic SDK with system instruction
  /// Limits the number of messages sent to the LLM to the last 25 messages
  /// to save LLM token input and reduce API costs
  Future<ChatResponse> chatWithSystemInstruction({
    required List<ChatMessage> messages,
    required String systemInstruction,
  }) async {
    try {
      Logger.debug(
        '======= GEMINI CHAT SERVICE WITH SYSTEM INSTRUCTION DEBUGGING =======',
      );
      Logger.debug(
        'Sending chat request to Gemini API via Firebase AI Logic SDK with system instruction',
      );
      Logger.debug('Received ${messages.length} messages for chat');

      // Check if we have any messages
      if (messages.isEmpty) {
        Logger.debug('CRITICAL ERROR: No messages provided to Gemini chat!');
        return ChatResponse.failure(
          errorMessage: 'No messages provided to chat',
        );
      }

      // Limit to the last 25 messages to save LLM token input and API cost
      final limitedMessages =
          messages.length > 25
              ? messages.sublist(messages.length - 25)
              : messages;

      Logger.debug('Using ${limitedMessages.length} messages after limiting');

      // Format messages for the API - Gemini uses a different format than Gemma
      final formattedParts =
          limitedMessages.map((message) {
            // Convert from ChatMessage.MessageRole to our local MessageRole enum
            final isUserMessage = message.role.toString() == 'MessageRole.user';
            return {
              'role': isUserMessage ? 'user' : 'model',
              'parts': [
                {'text': message.content},
              ],
            };
          }).toList();

      // Log the messages for debugging
      if (messages.length > 25) {
        Logger.debug(
          'Limited chat history from ${messages.length} to 25 messages to save LLM token input and API cost',
        );
      }
      Logger.debug('Formatted messages for Gemini API:');
      for (int i = 0; i < formattedParts.length; i++) {
        final msg = formattedParts[i];
        final parts = msg['parts'] as List<dynamic>?;
        final content =
            parts?.isNotEmpty == true ? parts![0]['text'] : 'No content';
        final preview =
            content.toString().length > 100
                ? '${content.toString().substring(0, 100)}...'
                : content.toString();
        Logger.debug('[$i] Role: ${msg['role']}, Content: $preview');
      }

      // Validate alternating pattern (should be user, model, user, model, etc.)
      for (int i = 1; i < formattedParts.length; i++) {
        final currentRole = formattedParts[i]['role'];
        final previousRole = formattedParts[i - 1]['role'];
        if (currentRole == previousRole) {
          Logger.error(
            'WARNING: Found consecutive $currentRole messages at positions ${i - 1} and $i. This may cause API issues.',
          );
        }
      }

      // Get model name and generation config from Remote Config
      final modelName = _llmModelConfig.getChatModel(userTier: _userTier);
      final remoteConfigMap = _llmModelConfig.getChatConfig(
        userTier: _userTier,
      );

      // Create GenerationConfig from remote config
      final baseGenerationConfig = GenerationConfig(
        candidateCount: remoteConfigMap['candidateCount'] as int?,
        maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
        temperature: remoteConfigMap['temperature'] as double?,
        topP: remoteConfigMap['topP'] as double?,
        topK: remoteConfigMap['topK'] as int?,
      );

      // Convert messages to SDK format
      final List<Content> sdkMessages = _formatMessagesForSdk(limitedMessages);

      // Get the response schema for chat
      final responseSchema = _firebaseAiProvider.getChatResponseSchema();

      // Get the model configured for chat
      final model = _firebaseAiProvider.getChatModel(
        modelName: modelName,
        responseSchema: responseSchema,
        systemInstructionText: systemInstruction,
        baseGenerationConfig: baseGenerationConfig,
      );

      Logger.debug('Calling Firebase AI Logic SDK for chat with system instruction. Model: $modelName');

      // Call the Firebase AI Logic SDK
      final response = await model.generateContent(sdkMessages);

      Logger.debug('Firebase AI Logic SDK response: ${response.text}');

      if (response.text == null || response.text!.isEmpty) {
        return ChatResponse.failure(
          errorMessage: 'No text in response from Firebase AI Logic SDK',
        );
      }

      // Extract the message content
      final rawContent = response.text!;

      // Ensure proper encoding of the content
      // This helps with non-ASCII characters like Chinese
      final decodedContent = _ensureProperEncoding(rawContent);

      // Try to parse the JSON response from the LLM with robust error handling
      // Initialize with the decoded content as fallback
      String finalContent = decodedContent;
      Map<String, dynamic>? parsedJson;

      try {
        // Check if the content looks like JSON
        if (decodedContent.trim().startsWith('{') &&
            decodedContent.trim().endsWith('}')) {
          // First attempt: try parsing as-is
          try {
            parsedJson = json.decode(decodedContent) as Map<String, dynamic>?;
            Logger.debug('Successfully parsed JSON on first attempt');
          } catch (e) {
            Logger.debug('First JSON parse attempt failed: $e');

            // Second attempt: try to fix common JSON issues
            String cleanedContent = _cleanJsonContent(decodedContent);
            try {
              parsedJson = json.decode(cleanedContent) as Map<String, dynamic>?;
              Logger.debug('Successfully parsed JSON after cleaning');
            } catch (e2) {
              Logger.debug('Second JSON parse attempt failed: $e2');

              // Third attempt: try to extract JSON fields manually using regex
              parsedJson = _extractJsonFieldsManually(decodedContent);
              if (parsedJson != null) {
                Logger.debug('Successfully extracted JSON fields manually');
              } else {
                Logger.debug('Manual JSON extraction failed');
              }
            }
          }

          if (parsedJson != null && parsedJson.containsKey('response')) {
            // Extract the response field as the final content
            finalContent = parsedJson['response'] as String;
            Logger.debug(
              'Successfully extracted response from JSON: ${finalContent.substring(0, finalContent.length.clamp(0, 100))}${finalContent.length > 100 ? "..." : ""}',
            );
          } else {
            Logger.debug(
              'JSON parsed but no response field found, using full content',
            );
          }
        } else {
          Logger.debug('Content does not appear to be JSON, using as-is');
        }
      } catch (e) {
        Logger.error('Error parsing JSON from LLM response', e);
        // Continue with the decoded content as fallback
      }

      Logger.debug(
        '======= END GEMINI CHAT WITH SYSTEM INSTRUCTION DEBUGGING =======',
      );

      return ChatResponse.success(
        content: finalContent,
        originalJson: parsedJson,
      );
    } catch (e) {
      Logger.error(
        'Error in Gemini chat request with system instruction via Firebase AI Logic SDK',
        e,
      );

      // Create a more detailed error message
      String errorMessage = 'Error in chat request: $e';

      // Check for specific error types and provide more helpful messages
      if (e is FirebaseAIException) {
        errorMessage = 'Firebase AI SDK error: ${e.message}';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'The request timed out. Please try again.';
      } else if (e.toString().contains('network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      }

      return ChatResponse.failure(errorMessage: errorMessage);
    }
  }

  /// Send a chat request to the Gemini API via Firebase AI Logic SDK
  /// Limits the number of messages sent to the LLM to the last 25 messages
  /// to save LLM token input and reduce API costs
  Future<ChatResponse> chat(List<ChatMessage> messages) async {
    try {
      Logger.debug('======= GEMINI CHAT SERVICE DEBUGGING =======');
      Logger.debug(
        'Sending chat request to Gemini API via Firebase Cloud Function',
      );
      Logger.debug('Received ${messages.length} messages for chat');

      // Check if we have any messages
      if (messages.isEmpty) {
        Logger.debug('CRITICAL ERROR: No messages provided to Gemini chat!');
        return ChatResponse.failure(
          errorMessage: 'No messages provided to chat',
        );
      }

      // Limit to the last 25 messages to save LLM token input and API cost
      final limitedMessages =
          messages.length > 25
              ? messages.sublist(messages.length - 25)
              : messages;

      Logger.debug('Using ${limitedMessages.length} messages after limiting');

      // Log the messages for debugging
      if (messages.length > 25) {
        Logger.debug(
          'Limited chat history from ${messages.length} to 25 messages to save LLM token input and API cost',
        );
      }

      // Check if the first message contains the ideas section
      if (limitedMessages.isNotEmpty) {
        // Check if it's a user message
        final isUserMessage =
            limitedMessages[0].role.toString() == 'MessageRole.user';
        if (isUserMessage) {
          final content = limitedMessages[0].content;
          Logger.debug('Checking first message for Ideas section:');
          if (content.contains('Ideas captured:')) {
            Logger.debug(
              'First message contains Ideas section, this is likely a context message',
            );
          }
        }
      }

      // Get model name and generation config from Remote Config
      final modelName = _llmModelConfig.getChatModel(userTier: _userTier);
      final remoteConfigMap = _llmModelConfig.getChatConfig(
        userTier: _userTier,
      );

      // Create GenerationConfig from remote config
      final baseGenerationConfig = GenerationConfig(
        candidateCount: remoteConfigMap['candidateCount'] as int?,
        maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
        temperature: remoteConfigMap['temperature'] as double?,
        topP: remoteConfigMap['topP'] as double?,
        topK: remoteConfigMap['topK'] as int?,
      );

      // Convert messages to SDK format
      final List<Content> sdkMessages = _formatMessagesForSdk(limitedMessages);

      // Get the response schema for chat
      final responseSchema = _firebaseAiProvider.getChatResponseSchema();

      // Get the model configured for chat
      final model = _firebaseAiProvider.getChatModel(
        modelName: modelName,
        responseSchema: responseSchema,
        baseGenerationConfig: baseGenerationConfig,
      );

      Logger.debug('Calling Firebase AI Logic SDK for chat. Model: $modelName');

      // Call the Firebase AI Logic SDK
      final response = await model.generateContent(sdkMessages);

      Logger.debug('Firebase AI Logic SDK response: ${response.text}');

      if (response.text == null || response.text!.isEmpty) {
        return ChatResponse.failure(
          errorMessage: 'No text in response from Firebase AI Logic SDK',
        );
      }

      // Extract the message content
      final rawContent = response.text!;

      // Ensure proper encoding of the content
      // This helps with non-ASCII characters like Chinese
      final decodedContent = _ensureProperEncoding(rawContent);

      // Try to parse the JSON response from the LLM with robust error handling
      // Initialize with the decoded content as fallback
      String finalContent = decodedContent;
      Map<String, dynamic>? parsedJson;

      try {
        // Check if the content looks like JSON
        if (decodedContent.trim().startsWith('{') &&
            decodedContent.trim().endsWith('}')) {
          // First attempt: try parsing as-is
          try {
            parsedJson = json.decode(decodedContent) as Map<String, dynamic>?;
            Logger.debug('Successfully parsed JSON on first attempt');
          } catch (e) {
            Logger.debug('First JSON parse attempt failed: $e');

            // Second attempt: try to fix common JSON issues
            String cleanedContent = _cleanJsonContent(decodedContent);
            try {
              parsedJson = json.decode(cleanedContent) as Map<String, dynamic>?;
              Logger.debug('Successfully parsed JSON after cleaning');
            } catch (e2) {
              Logger.debug('Second JSON parse attempt failed: $e2');

              // Third attempt: try to extract JSON fields manually using regex
              parsedJson = _extractJsonFieldsManually(decodedContent);
              if (parsedJson != null) {
                Logger.debug('Successfully extracted JSON fields manually');
              } else {
                Logger.debug('Manual JSON extraction failed');
              }
            }
          }

          if (parsedJson != null && parsedJson.containsKey('response')) {
            // Extract the response field as the final content
            finalContent = parsedJson['response'] as String;
            Logger.debug(
              'Successfully extracted response from JSON: ${finalContent.substring(0, finalContent.length.clamp(0, 100))}${finalContent.length > 100 ? "..." : ""}',
            );
          } else {
            Logger.debug(
              'JSON parsed but no response field found, using full content',
            );
          }
        } else {
          Logger.debug('Content does not appear to be JSON, using as-is');
        }
      } catch (e) {
        Logger.error('Error parsing JSON from LLM response', e);
        // Continue with the decoded content as fallback
      }

      Logger.debug('Successfully received response from Gemini API');
      Logger.debug(
        'Final response content length: ${finalContent.length} characters',
      );
      Logger.debug('======= END GEMINI CHAT SERVICE DEBUGGING =======');

      return ChatResponse.success(
        content: finalContent,
        originalJson: parsedJson,
      );
    } catch (e) {
      Logger.error(
        'Error in Gemini chat request via Firebase AI Logic SDK',
        e,
      );

      // Create a more detailed error message
      String errorMessage = 'Error in chat request: $e';

      // Check for specific error types and provide more helpful messages
      if (e is FirebaseAIException) {
        errorMessage = 'Firebase AI SDK error: ${e.message}';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'The request timed out. Please try again.';
      } else if (e.toString().contains('network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      }

      return ChatResponse.failure(errorMessage: errorMessage);
    }
  }

  /// Generate suggested prompts based on ideabook content
  Future<ChatResponse> generateSuggestedPrompts(String promptContent) async {
    try {
      Logger.debug('======= GEMINI SUGGESTED PROMPTS DEBUGGING =======');
      Logger.debug('Generating suggested prompts with Gemini API');

      // Get model name and generation config from Remote Config
      final modelName = _llmModelConfig.getSuggestedPromptsModel(
        userTier: _userTier,
      );
      final remoteConfigMap = _llmModelConfig.getSuggestedPromptsConfig(
        userTier: _userTier,
      );

      // Create GenerationConfig from remote config
      final baseGenerationConfig = GenerationConfig(
        candidateCount: remoteConfigMap['candidateCount'] as int?,
        maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
        temperature: remoteConfigMap['temperature'] as double?,
        topP: remoteConfigMap['topP'] as double?,
        topK: remoteConfigMap['topK'] as int?,
      );

      // Get the response schema for suggested prompts
      final responseSchema = _firebaseAiProvider.getSuggestedPromptsResponseSchema();

      // Get the model configured for suggested prompts
      final model = _firebaseAiProvider.getChatModel(
        modelName: modelName,
        responseSchema: responseSchema,
        baseGenerationConfig: baseGenerationConfig,
      );

      // Create a single message with the prompt content
      final sdkMessages = [
        Content('user', [TextPart(promptContent)]),
      ];

      Logger.debug(
        'Calling Firebase AI Logic SDK for suggested prompts. Model: $modelName',
      );

      // Call the Firebase AI Logic SDK
      final response = await model.generateContent(sdkMessages);

      Logger.debug('Firebase AI Logic SDK response: ${response.text}');

      if (response.text == null || response.text!.isEmpty) {
        return ChatResponse.failure(
          errorMessage: 'No text in response from Firebase AI Logic SDK',
        );
      }

      // Extract the message content
      final rawContent = response.text!;

      // Ensure proper encoding of the content
      final decodedContent = _ensureProperEncoding(rawContent);

      Logger.debug('Successfully received suggested prompts from Gemini API');
      Logger.debug('Raw response content: $decodedContent');

      // Try to parse as JSON
      Map<String, dynamic>? parsedJson;
      try {
        parsedJson = json.decode(decodedContent) as Map<String, dynamic>?;
        Logger.debug('Successfully parsed JSON from content');
      } catch (e) {
        Logger.error('Error parsing JSON from content', e);
        return ChatResponse.failure(
          errorMessage: 'Error parsing JSON response: $e',
        );
      }

      Logger.debug('======= END GEMINI SUGGESTED PROMPTS DEBUGGING =======');

      // Return the parsed JSON content
      return ChatResponse.success(
        content: decodedContent,
        originalJson: parsedJson,
      );
    } catch (e) {
      Logger.error('Error generating suggested prompts', e);

      // Create a more detailed error message
      String errorMessage = 'Error generating suggested prompts: $e';

      // Check for specific error types and provide more helpful messages
      if (e is FirebaseAIException) {
        errorMessage = 'Firebase AI SDK error: ${e.message}';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'The request timed out. Please try again.';
      } else if (e.toString().contains('network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      }

      return ChatResponse.failure(errorMessage: errorMessage);
    }
  }
}
