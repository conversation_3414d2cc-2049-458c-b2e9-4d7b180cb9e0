import 'dart:convert';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:noeji/models/chat_message.dart';
import 'package:noeji/services/firebase/cloud_function_service.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';

/// Response from the Gemma chat API
class ChatResponse {
  /// Whether the request was successful
  final bool isSuccess;

  /// The response message content
  final String? content;

  /// Error message if the request failed
  final String? errorMessage;

  /// Original JSON data from the LLM response
  final Map<String, dynamic>? originalJson;

  /// Creates a new ChatResponse for a successful request
  const ChatResponse.success({required this.content, this.originalJson})
    : isSuccess = true,
      errorMessage = null;

  /// Creates a new ChatResponse for a failed request
  const ChatResponse.failure({required this.errorMessage})
    : isSuccess = false,
      content = null,
      originalJson = null;
}

/// Service for interacting with the Gemma API
class GemmaService {
  /// The model to use for chat
  final String model;

  /// The Cloud Function service
  final CloudFunctionService _cloudFunctionService;

  /// Constructor
  GemmaService({
    this.model = 'gemma-3-27b-it',
    CloudFunctionService? cloudFunctionService,
  }) : _cloudFunctionService = cloudFunctionService ?? CloudFunctionService();

  /// Send a chat request to the Gemma API via Firebase Cloud Function
  /// Limits the number of messages sent to the LLM to the last 25 messages
  /// to save LLM token input and reduce API costs
  Future<ChatResponse> chat(List<ChatMessage> messages) async {
    try {
      Logger.debug('======= GEMMA SERVICE DEBUGGING =======');
      Logger.debug(
        'Sending chat request to Gemma API via Firebase Cloud Function',
      );
      Logger.debug('Received ${messages.length} messages for chat');

      // Check if we have any messages
      if (messages.isEmpty) {
        Logger.debug('CRITICAL ERROR: No messages provided to Gemma chat!');
        return ChatResponse.failure(
          errorMessage: 'No messages provided to chat',
        );
      }

      // Log the original messages
      Logger.debug('Original messages:');
      for (int i = 0; i < messages.length; i++) {
        final msg = messages[i];
        final contentPreview =
            msg.content.length > 100
                ? '${msg.content.substring(0, 100)}...'
                : msg.content;
        Logger.debug(
          'Message $i - Role: ${msg.role}, Content: "$contentPreview"',
        );
      }

      // Limit to the last 25 messages to save LLM token input and API cost
      final limitedMessages =
          messages.length > 25
              ? messages.sublist(messages.length - 25)
              : messages;

      Logger.debug('Using ${limitedMessages.length} messages after limiting');

      // Format messages for the API - Gemma uses a different format than Grok
      final formattedParts =
          limitedMessages
              .map(
                (message) => {
                  'role':
                      message.role == MessageRole.user ? 'user' : 'assistant',
                  'parts': [
                    {'text': message.content},
                  ],
                },
              )
              .toList();

      // Log the messages for debugging
      if (messages.length > 25) {
        Logger.debug(
          'Limited chat history from ${messages.length} to 25 messages to save LLM token input and API cost',
        );
      }

      // Check if the first message contains the ideas section
      if (limitedMessages.isNotEmpty &&
          limitedMessages[0].role == MessageRole.user) {
        final content = limitedMessages[0].content;
        Logger.debug('Checking first message for Ideas section:');

        // Check if the message contains the Ideas section
        if (content.contains('Ideas:')) {
          final ideasIndex = content.indexOf('Ideas:');
          final userPromptIndex = content.indexOf('User\'s prompt:');

          if (ideasIndex >= 0 && userPromptIndex > ideasIndex) {
            // Extract the Ideas section
            final ideasSection =
                content
                    .substring(ideasIndex + 'Ideas:'.length, userPromptIndex)
                    .trim();

            Logger.debug('Ideas section found:');
            Logger.debug(ideasSection);

            // Check if the Ideas section is empty or just contains "No ideas found"
            if (ideasSection.isEmpty ||
                ideasSection == 'No ideas found in this ideabook yet.') {
              Logger.debug(
                'WARNING: Ideas section is empty or just contains placeholder text!',
              );
            } else {
              // Count the number of ideas (roughly by counting lines)
              final ideaLines =
                  ideasSection
                      .split('\n')
                      .where((line) => line.trim().isNotEmpty)
                      .length;
              Logger.debug(
                'Found approximately $ideaLines ideas in the Ideas section',
              );
            }
          } else {
            Logger.debug('WARNING: Ideas section format is unexpected!');
          }
        } else {
          Logger.debug('WARNING: No Ideas section found in the first message!');
        }
      }

      Logger.debug('Formatted messages for Gemma API:');
      for (var msg in formattedParts) {
        final role = msg['role'] as String?;
        final parts = msg['parts'] as List<dynamic>?;
        final text =
            parts != null && parts.isNotEmpty
                ? (parts[0]['text'] as String?) ?? 'null'
                : 'null';
        final contentPreview =
            text.length > 100 ? '${text.substring(0, 100)}...' : text;
        Logger.debug('Role: $role, Content: "$contentPreview"');
      }

      // Create the request body for Gemma API
      final requestBody = {
        'contents': formattedParts,
        'model': model,
        'generationConfig': {
          'temperature':
              0.9, // Increased temperature for even more creative responses in chat
        },
      };

      Logger.debug('Gemma API request body: $requestBody');

      // Call the Firebase Cloud Function
      final response = await _cloudFunctionService.callLlmApi(
        provider: 'gemma',
        requestBody: requestBody,
      );

      if (!response.isSuccess || response.data == null) {
        Logger.error(
          'Gemma API error via Firebase Cloud Function: ${response.errorMessage}',
        );

        // Log detailed error information if available
        if (response.details != null) {
          final location =
              response.details!['location'] as String? ?? 'unknown';
          final timestamp = response.details!['timestamp'] as String? ?? '';
          final stack = response.details!['stack'] as String? ?? '';

          Logger.error('Error details - Location: $location, Time: $timestamp');
          Logger.error('Stack trace: $stack');
        }

        return ChatResponse.failure(
          errorMessage: response.errorMessage ?? 'Unknown error',
        );
      }

      // Parse the response
      final jsonResponse = response.data;
      Logger.debug(
        'Gemma API response via Firebase Cloud Function: $jsonResponse',
      );

      // Extract the message content
      final rawContent =
          jsonResponse['candidates'][0]['content']['parts'][0]['text'];

      // Ensure proper encoding of the content
      // This helps with non-ASCII characters like Chinese
      final decodedContent = _ensureProperEncoding(rawContent);

      // Try to parse the JSON response from the LLM
      // Initialize with the decoded content as fallback
      String finalContent = decodedContent;

      // First, check if the content is wrapped in markdown code blocks
      String contentToProcess = decodedContent;

      // Check for markdown code blocks with ```json or ``` pattern
      if (contentToProcess.trim().startsWith('```')) {
        Logger.debug(
          'Content appears to be wrapped in markdown code blocks, extracting...',
        );

        // Find the end of the code block
        final endBlockIndex = contentToProcess.lastIndexOf('```');
        if (endBlockIndex > 3) {
          // Extract content between the code blocks
          final startContentIndex =
              contentToProcess.indexOf('\n', contentToProcess.indexOf('```')) +
              1;
          if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
            contentToProcess =
                contentToProcess
                    .substring(startContentIndex, endBlockIndex)
                    .trim();
            Logger.debug(
              'Extracted content from markdown code block: ${contentToProcess.substring(0, contentToProcess.length.clamp(0, 100))}${contentToProcess.length > 100 ? "..." : ""}',
            );
          }
        }
      }

      // Variable to store the parsed JSON
      Map<String, dynamic>? parsedJson;
      bool foundResponse = false;

      // First check: direct JSON format
      if (contentToProcess.trim().startsWith('{') &&
          contentToProcess.trim().endsWith('}')) {
        try {
          // Check if the content is valid JSON
          parsedJson = json.decode(contentToProcess) as Map<String, dynamic>;

          // Extract the response field if it exists
          if (parsedJson.containsKey('response')) {
            finalContent = parsedJson['response'] as String;
            foundResponse = true;
            Logger.debug(
              'Successfully extracted response field from JSON: ${finalContent.substring(0, finalContent.length.clamp(0, 100))}${finalContent.length > 100 ? "..." : ""}',
            );

            // Check if user_prompt exists and log it
            if (parsedJson.containsKey('user_prompt')) {
              final userPrompt = parsedJson['user_prompt'] as String;
              Logger.debug(
                'Successfully extracted user_prompt field from JSON: $userPrompt',
              );
            } else {
              Logger.debug('No user_prompt field found in JSON');
            }
          } else {
            // Try to find response field in nested objects
            parsedJson.forEach((key, value) {
              if (value is Map<String, dynamic> &&
                  value.containsKey('response')) {
                finalContent = value['response'] as String;
                foundResponse = true;
                Logger.debug(
                  'Successfully extracted response field from nested JSON',
                );
              }
            });

            if (!foundResponse) {
              // If no response field, use the whole content
              Logger.debug(
                'No response field found in JSON, using raw content',
              );
              finalContent = decodedContent;
              parsedJson =
                  null; // Reset parsedJson since it doesn't have the expected format
            }
          }
        } catch (e) {
          // If parsing fails, try more aggressive approaches
          Logger.debug(
            'Initial JSON parsing failed: $e, trying alternative approaches',
          );

          // Default to raw content in case all parsing attempts fail
          finalContent = decodedContent;
          parsedJson = null;
        }
      } else {
        // Not JSON format, use raw content
        finalContent = decodedContent;
        parsedJson = null;
      }

      // Second check: Try to find any JSON-like structure in the content if we haven't found a response yet
      if (!foundResponse && contentToProcess.contains('"response"')) {
        // Look for patterns like {"response": "..."}
        final responsePattern = RegExp(
          r'[\s\S]*?"response"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
        );
        final match = responsePattern.firstMatch(contentToProcess);

        if (match != null && match.groupCount >= 1) {
          finalContent = match.group(1) ?? finalContent;
          foundResponse = true;
          Logger.debug('Extracted response using regex pattern matching');

          // Try to reconstruct a parsedJson for metadata
          try {
            parsedJson = {'response': finalContent};

            // Try to extract user_prompt using regex as well
            final promptPattern = RegExp(
              r'[\s\S]*?"user_prompt"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
            );
            final promptMatch = promptPattern.firstMatch(contentToProcess);

            if (promptMatch != null && promptMatch.groupCount >= 1) {
              final userPrompt = promptMatch.group(1);
              if (userPrompt != null) {
                parsedJson['user_prompt'] = userPrompt;
                Logger.debug(
                  'Extracted user_prompt using regex pattern matching: $userPrompt',
                );
              }
            }
          } catch (e) {
            Logger.debug(
              'Failed to reconstruct parsedJson after regex extraction: $e',
            );
          }
        }
      }

      // Third check: If it still looks like JSON but we couldn't extract the response
      if (!foundResponse &&
          contentToProcess.contains('"response"') &&
          contentToProcess.contains('{') &&
          contentToProcess.contains('}')) {
        try {
          // Try to clean up the JSON string
          String cleanedJson =
              contentToProcess
                  .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                  .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                  .trim();

          // Find the start and end of what looks like a JSON object
          int startIdx = cleanedJson.indexOf('{');
          int endIdx = cleanedJson.lastIndexOf('}') + 1;

          if (startIdx >= 0 && endIdx > startIdx) {
            cleanedJson = cleanedJson.substring(startIdx, endIdx);

            // Try to parse the cleaned JSON
            final jsonResponse =
                json.decode(cleanedJson) as Map<String, dynamic>;

            if (jsonResponse.containsKey('response')) {
              finalContent = jsonResponse['response'] as String;
              parsedJson = jsonResponse;
              foundResponse = true;
              Logger.debug(
                'Successfully extracted response field from cleaned JSON',
              );
            }
          }
        } catch (e) {
          Logger.debug('Cleaned JSON parsing failed: $e');
        }
      }

      Logger.debug('Successfully received response from Gemma API');
      Logger.debug(
        'Final response content length: ${finalContent.length} characters',
      );
      Logger.debug('======= END GEMMA SERVICE DEBUGGING =======');

      return ChatResponse.success(
        content: finalContent,
        originalJson: parsedJson,
      );
    } catch (e) {
      Logger.error(
        'Error in Gemma chat request via Firebase Cloud Function',
        e,
      );

      // Create a more detailed error message
      String errorMessage = 'Error in chat request: $e';

      // Check if it's a FirebaseFunctionsException
      if (e is FirebaseFunctionsException) {
        // Extract more details if available
        final details = e.details as Map<String, dynamic>?;
        if (details != null) {
          final location = details['location'] as String? ?? 'unknown';
          final timestamp = details['timestamp'] as String? ?? '';
          final stack = details['stack'] as String? ?? '';

          Logger.error('Firebase Function error details:');
          Logger.error('Location: $location');
          Logger.error('Timestamp: $timestamp');
          Logger.error('Stack: $stack');

          // Check if this is a permission error
          final isPermissionError =
              ErrorUtils.isPermissionError(e) ||
              (e.code == 'permission-denied') ||
              (e.code == 'PERMISSION_DENIED');

          if (isPermissionError) {
            // Use a generic permission denied message
            errorMessage = ErrorUtils.permissionDeniedMessage;
          } else {
            // Create a more informative error message for other errors
            errorMessage = 'Error in $location: ${e.message}';
          }
        }
      } else if (ErrorUtils.isPermissionError(e)) {
        // Handle other permission errors
        errorMessage = ErrorUtils.permissionDeniedMessage;
      }

      Logger.debug('======= END GEMMA SERVICE DEBUGGING (ERROR) =======');
      return ChatResponse.failure(errorMessage: errorMessage);
    }
  }

  /// Ensure proper encoding of the content
  /// This helps with non-ASCII characters like Chinese
  String _ensureProperEncoding(String content) {
    try {
      // First, check if the content is already valid UTF-8
      if (_isValidUtf8(content)) {
        return content;
      }

      // If not, try to decode and re-encode the content
      final decoded = utf8.decode(utf8.encode(content));
      return decoded;
    } catch (e) {
      Logger.error('Error ensuring proper encoding: $e');
      return content; // Return original content if encoding fails
    }
  }

  /// Check if a string is valid UTF-8
  bool _isValidUtf8(String text) {
    try {
      utf8.decode(utf8.encode(text));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Dispose method (kept for backward compatibility)
  void dispose() {
    // No HTTP client to dispose anymore
  }

  /// Generate suggested prompts based on ideabook content
  Future<ChatResponse> generateSuggestedPrompts(String promptContent) async {
    try {
      Logger.debug('======= GEMMA SUGGESTED PROMPTS DEBUGGING =======');
      Logger.debug('Generating suggested prompts with Gemma API');

      // Create a single message with the prompt content
      final formattedParts = [
        {
          'role': 'user',
          'parts': [
            {'text': promptContent},
          ],
        },
      ];

      // Create the request body for Gemma API
      final requestBody = {
        'contents': formattedParts,
        'model': model,
        'generationConfig': {
          'temperature':
              0.7, // Increased temperature for more creative and diverse prompt suggestions
        },
      };

      Logger.debug(
        'Gemma API request body for suggested prompts: $requestBody',
      );

      // Call the Firebase Cloud Function
      final response = await _cloudFunctionService.callLlmApi(
        provider: 'gemma',
        requestBody: requestBody,
      );

      if (!response.isSuccess || response.data == null) {
        Logger.error(
          'Gemma API error for suggested prompts: ${response.errorMessage}',
        );
        return ChatResponse.failure(
          errorMessage: response.errorMessage ?? 'Unknown error',
        );
      }

      // Parse the response
      final jsonResponse = response.data;
      Logger.debug('Gemma API response for suggested prompts: $jsonResponse');

      // Extract the message content
      final rawContent =
          jsonResponse['candidates'][0]['content']['parts'][0]['text'];

      // Ensure proper encoding of the content
      final decodedContent = _ensureProperEncoding(rawContent);

      Logger.debug('Successfully received suggested prompts from Gemma API');
      Logger.debug('Raw response content: $decodedContent');

      // Process the content to extract JSON if needed
      String processedContent = decodedContent;

      // Check for markdown code blocks with ```json or ``` pattern
      if (processedContent.trim().startsWith('```')) {
        Logger.debug(
          'Content appears to be wrapped in markdown code blocks, extracting...',
        );

        // Find the end of the code block
        final endBlockIndex = processedContent.lastIndexOf('```');
        if (endBlockIndex > 3) {
          // Extract content between the code blocks
          final startContentIndex =
              processedContent.indexOf('\n', processedContent.indexOf('```')) +
              1;
          if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
            processedContent =
                processedContent
                    .substring(startContentIndex, endBlockIndex)
                    .trim();
            Logger.debug(
              'Extracted content from markdown code block: ${processedContent.substring(0, processedContent.length.clamp(0, 100))}${processedContent.length > 100 ? "..." : ""}',
            );
          }
        }
      }

      // Enhanced JSON detection and parsing for suggested prompts
      bool foundJson = false;
      Map<String, dynamic>? parsedJson;

      // First check: direct JSON format
      if (processedContent.trim().startsWith('{') &&
          processedContent.trim().endsWith('}')) {
        try {
          // Try to parse the JSON
          parsedJson = json.decode(processedContent) as Map<String, dynamic>;
          foundJson = true;
          Logger.debug('Successfully parsed JSON for suggested prompts');
        } catch (e) {
          // If parsing fails, try more aggressive approaches
          Logger.debug(
            'Initial JSON parsing failed for suggested prompts: $e, trying alternative approaches',
          );
        }
      }

      // Second check: If it still looks like JSON but we couldn't parse it
      if (!foundJson &&
          processedContent.contains('{') &&
          processedContent.contains('}')) {
        try {
          // Try to clean up the JSON string
          String cleanedJson =
              processedContent
                  .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                  .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                  .trim();

          // Find the start and end of what looks like a JSON object
          int startIdx = cleanedJson.indexOf('{');
          int endIdx = cleanedJson.lastIndexOf('}') + 1;

          if (startIdx >= 0 && endIdx > startIdx) {
            cleanedJson = cleanedJson.substring(startIdx, endIdx);

            // Try to parse the cleaned JSON
            parsedJson = json.decode(cleanedJson) as Map<String, dynamic>;
            foundJson = true;
            Logger.debug(
              'Successfully parsed cleaned JSON for suggested prompts',
            );
          }
        } catch (e) {
          Logger.debug('Cleaned JSON parsing failed for suggested prompts: $e');
        }
      }

      Logger.debug('======= END GEMMA SUGGESTED PROMPTS DEBUGGING =======');

      // If we successfully parsed JSON, return it as is
      // The SuggestedPromptsProvider will handle extracting the prompts
      if (foundJson && parsedJson != null) {
        return ChatResponse.success(
          content: processedContent,
          originalJson: parsedJson,
        );
      }

      // Otherwise return the processed content
      return ChatResponse.success(content: processedContent);
    } catch (e) {
      Logger.error('Error generating suggested prompts', e);
      return ChatResponse.failure(
        errorMessage: 'Error generating suggested prompts: $e',
      );
    }
  }
}
